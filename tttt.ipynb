{"cells": [{"cell_type": "code", "execution_count": 2, "id": "e3080deb", "metadata": {}, "outputs": [], "source": ["import numpy as np"]}, {"cell_type": "code", "execution_count": 4, "id": "a281a9e6", "metadata": {}, "outputs": [], "source": ["a = np.ones((2,3,4))"]}, {"cell_type": "code", "execution_count": 7, "id": "73203fd8", "metadata": {}, "outputs": [], "source": ["a = a.reshape(-1,a.shape[-1])"]}, {"cell_type": "code", "execution_count": 10, "id": "24373788", "metadata": {}, "outputs": [], "source": ["b = np.zeros((2,3,4))\n", "b = b.reshape(-1,b.shape[-1])\n", "c = np.concatenate((a,b),axis = 0)"]}, {"cell_type": "code", "execution_count": 12, "id": "52dd24da", "metadata": {}, "outputs": [{"data": {"text/plain": ["np.int64(6)"]}, "execution_count": 12, "metadata": {}, "output_type": "execute_result"}], "source": ["np.sum(np.all(c == np.ones(4),axis = 1))"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.13.5"}}, "nbformat": 4, "nbformat_minor": 5}