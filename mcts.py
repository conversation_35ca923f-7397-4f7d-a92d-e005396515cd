import numpy as np
import random
import math
from agent import Agent

class Search(Agent):
    def __init__(self, player):
        super().__init__(player)
        self.win_patterns = [self.player,self.player,self.player,self.player,self.player]
        self.lose_patterns = [self.opponent,self.opponent,self.opponent,self.opponent,self.opponent]

    def make_move(self, board):
        # Use MCTS to find the best move
        best_move = None
        best_score = float('-inf')

        possible_moves = self.get_next(board)
        if not possible_moves:
            return None

        for move in possible_moves:
            new_board = self.get_new_board(board, move, self.player)
            score = self.mcts(new_board, 3, self.opponent)  # Search depth of 3
            if score > best_score:
                best_score = score
                best_move = move

        return best_move

    def get_next(self,board):
        if np.all(board==0):
            return [(board.shape[0]//2,board.shape[1]//2)]
        else:
            return [
                (i,j)
                for i in range(board.shape[0])
                for j in range(board.shape[1])
                if np.sum(board[max(0,i-1):i+2,max(0,j-1):j+2]) != 0 and board[i,j] == 0
            ]
    def check_win(self, board):
        window_1 = np.lib.stride_tricks.sliding_window_view(board,5,axis=0)
        window_2 = np.lib.stride_tricks.sliding_window_view(board,5,axis=1)
        window_3 = np.lib.stride_tricks.sliding_window_view(np.diag(board),5)
        window_4 = np.lib.stride_tricks.sliding_window_view(np.diag(np.fliplr(board)),5)
        window = np.concatenate((window_1.reshape(-1,5),window_2.reshape(-1,5),window_3.reshape(-1,5),window_4.reshape(-1,5)),axis=0)
        if np.any(np.all(window==self.win_patterns,axis=1)):
            return 1
        elif np.any(np.all(window==self.lose_patterns,axis=1)):
            return -1
        else:
            return 0
    def get_new_board(self,org_board,pos,current_player):
        new_board = org_board.copy()
        new_board[pos] = current_player
        return new_board 
    def mcts(self, board, limit_depth, current_player):
        # 使用UCT选择最好的后继结点，总搜索深度设为limit_depth，player+opponent==3,要求满足选择，扩展，模拟，回溯总流程。
        if limit_depth == 0:
            return self.check_win(board)

        # Check if game is already over
        game_result = self.check_win(board)
        if game_result != 0:
            return game_result

        # Get all possible moves
        possible_moves = self.get_next(board)
        if not possible_moves:
            return 0  # Draw

        # Selection and Expansion: Try all possible moves
        best_score = float('-inf') if current_player == self.player else float('inf')

        for move in possible_moves:
            # Create new board state
            new_board = self.get_new_board(board, move, current_player)

            # Simulation: Recursively search deeper
            next_player = 3 - current_player  # Switch player (1->2, 2->1)
            score = self.mcts(new_board, limit_depth - 1, next_player)

            # Backpropagation: Update best score based on current player
            if current_player == self.player:
                # Maximizing for our player
                best_score = max(best_score, score)
            else:
                # Minimizing for opponent
                best_score = min(best_score, score)

        return best_score