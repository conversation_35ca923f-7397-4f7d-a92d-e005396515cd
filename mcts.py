import numpy as np
import random
import math
from agent import Agent

class Search(Agent):
    def __init__(self, player):
        super().__init__(player)
        self.win_patterns = [self.player,self.player,self.player,self.player,self.player]
        self.lose_patterns = [self.opponent,self.opponent,self.opponent,self.opponent,self.opponent]

    def make_move(self, board):
        return 
    
    def get_next(self,board):
        if np.all(board==0):
            return [(board.shape[0]//2,board.shape[1]//2)]
        else:
            return [
                (i,j)
                for i in range(board.shape[0])
                for j in range(board.shape[1])
                if np.sum[max(0,i-1):i+2,max(0,j-1):j+2] != 0 and board[i,j] == 0
            ]
    def check_win(self, board):
        window_1 = np.lib.stride_tricks.sliding_window_view(board,5,axis=0)
        window_2 = np.lib.stride_tricks.sliding_window_view(board,5,axis=1)
        window_3 = np.lib.stride_tricks.sliding_window_view(np.diag(board),5)
        window_4 = np.lib.stride_tricks.sliding_window_view(np.diag(np.fliplr(board)),5)
        window = np.concatenate((window_1.reshape(-1,5),window_2.reshape(-1,5),window_3.reshape(-1,5),window_4.reshape(-1,5)),axis=0)
        if np.any(np.all(window==self.win_patterns,axis=1)):
            return 1
        elif np.any(np.all(window==self.lose_patterns,axis=1)):
            return -1
        else:
            return 0
    def get_new_board(self,org_board,pos,current_player):
        new_board = org_board.copy()
        new_board[pos] = current_player
        return new_board 
    def mcts(self,board,limit_depth,current_player):
        # 使用UCT选择最好的后继结点，总搜索深度设为limit_depth，player+opponent==3,要求满足选择，扩展，模拟，回溯总流程。
        if(limit_depth==0):
            return self.check_win(board)
        else:
            
        
            