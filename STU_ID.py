import numpy as np
from agent import Agent

class Search1(Agent):
    def __init__(self,player):
        super().__init__(player)
        self.max_depth = 2#最大搜索深度
        self.win_score = 100000#胜利分数
        self.eval_cache = {}#评估缓存

    def make_move(self,board):
        board=np.array(board)
        size=len(board)
        
        #若第一步，直接天元
        if np.all(board==0):
            certer=size//2
            return (certer,certer)
        
        best_move = None
        best_score = -float('inf')
        alpha = -float('inf')
        beta = float('inf')

        #获取所有可能的移动
        possible_moves = self.get_possible_moves(board)

        #迭代加深搜索
        for depth in range(1,self.max_depth+1):
            current_best_move=None
            current_best_score=-float('inf')

            for move in possible_moves:
                row,col=move

                #模拟移动
                board[row][col]=self.player

                score=self.minimax(board,depth-1,False,alpha,beta)
                
                #撤销移动
                board[row][col]=0

                if score > current_best_score:
                    current_best_score = score
                    current_best_move = move

                alpha=max(alpha,score)
                if beta<=alpha:
                    break
            
            #若找到更好的,则更新
            if current_best_move is not None and current_best_score > best_score:
                best_move=current_best_move
                best_score=current_best_score
        
        #若未找到最佳移动，则选择第一个可能的移动
        if best_move is None:
            best_move=possible_moves[0]

        return best_move
    
    def minimax(self,board,depth,is_maximizing,alpha,beta):

        #达到最大深度或游戏结束，评估棋盘
        if depth==0 or self.is_board_full(board):
            return self.evaluate_board(board)
        
        #获取所有可能移动
        possible_moves = self.get_possible_moves(board)
        if is_maximizing:
            best_score=float('-inf')
            for move in possible_moves:
                row,col=move
                
                #模拟移动
                board[row][col]=self.player

                score = self.minimax(board,depth-1,False,alpha,beta)
                
                #撤销移动
                board[row][col]=0

                best_score=max(best_score,score)

                alpha=max(alpha,score)

                if beta<=alpha:
                    break
            
            return best_score
        else:
            best_score=float('inf')
            for move in possible_moves:
                row,col=move
                
                #模拟移动
                board[row][col]=self.opponent

                score = self.minimax(board,depth-1,True,alpha,beta)
                
                #撤销移动
                board[row][col]=0

                best_score=min(best_score,score)

                beta=min(beta,score)

                if beta<=alpha:
                    break
            
            return best_score

    def get_possible_moves(self,board):
        size=len(board)
        moves=[]
        for i in range(size):
            for j in range(size):
                if(board[i][j]==0):
                    #放个自己棋，看赢不赢
                    board[i][j]=self.player
                    if self.check_win(board,i,j):
                        board[i][j]=0
                        return [(i,j)]
                    board[i][j]=0

                    #放个对手棋，看输不输
                    board[i][j]=self.opponent
                    if self.check_win(board,i,j):
                        board[i][j]=0
                        return [(i,j)]
                    board[i][j]=0
        
        #若无一招致胜或致败，返回所有空位
        for i in range(size):
            for j in range(size):
                if(board[i][j]==0):
                    moves.append((i,j))
        
        #按价值排序，价值高的放前面
        moves.sort(key=lambda move: self.evaluate_move(board, move),reverse=True)
        return moves
    def evaluate_move(self,board,move):
        row,col=move
        score=0
        size=len(board)

        #离中心越近，分数越高
        certer=size//2
        distance_to_center=abs(row-certer)+abs(col-certer)
        score +=distance_to_center*10

        #周围有有棋子的地方价值更高
        for i in range(max(0,row-2),min(size,row+3)):
            for j in range(max(0,col-2),min(size,col+3)):
                if board[i][j]!=0:
                    distance=abs(i-row)+abs(j-col)
                    if distance==1:
                        score+=100
                    elif distance==2:
                        score+=50
        return score
    
    def evaluate_board(self,board):
        board_hash=hash(board.tobytes())
        #若缓存中已有，直接返回
        if board_hash in self.eval_cache:
            return self.eval_cache[board_hash]
        
        score=0
        size=len(board)

        #评估每个行，列，对角线
        for i in range(size):
            #行
            row=board[i,:]
            score+=self.evaluate_line(row)

            #列
            col=board[:,i]
            score+=self.evaluate_line(col)
        
        for i in range(-size+5,size-4):
            #主对角线
            diag = np.diag(board,i)
            score+=self.evaluate_line(diag)
            
            #反对角线
            anti_diag=np.diag(np.fliplr(board),i)
            score+=self.evaluate_line(anti_diag)

        self.eval_cache[board_hash]=score

        return score
    def evaluate_line(self,line):
        score = 0
        length = len(line)

        for i in range(length-4):
            segment=line[i:i+5]
            score+=self.evaluate_segment(segment)
        return score
    def evaluate_segment(self,segment):
        player_count=np.sum(segment==self.player)
        opponent_count=np.sum(segment==self.opponent)

        #若都有棋子，该评估没有价值
        if player_count>0 and opponent_count>0:
            return 0
        
        #评估自己
        if player_count > 0:
            if player_count==5:
                return self.win_score
            elif player_count==4:
                return 10000
            elif player_count==3:
                return 1000
            elif player_count==2:
                return 100
            elif player_count==1:
                return 10
            
        #评估对方
        if opponent_count > 0:
            if opponent_count==5:
                return -self.win_score
            elif opponent_count==4:
                return -10000
            elif opponent_count==3:
                return -1000
            elif opponent_count==2:
                return -100
            elif opponent_count==1:
                return -10
            
        return 0
    def check_win(self, board, row, col):
    
        board_size = len(board)
        player = board[row][col]

        directions = [
            (0, 1),
            (1, 0),
            (1, 1),
            (1, -1),
        ]

        for dx, dy in directions:
            count = 1

            x, y = row + dx, col + dy
            while 0 <= x < board_size and 0 <= y < board_size and board[x][y] == player:
                count += 1
                x, y = x + dx, y + dy

            x, y = row - dx, col - dy
            while 0 <= x < board_size and 0 <= y < board_size and board[x][y] == player:
                count += 1
                x, y = x - dx, y - dy

            if count >= 5:
                return True

        return False
    
    def is_board_full(self ,board):
        
        return np.all(board != 0)