{"cells": [{"cell_type": "code", "execution_count": 23, "id": "dec3900f", "metadata": {}, "outputs": [], "source": ["import numpy as np\n", "\n", "def evaluate(board:np.n<PERSON><PERSON>,patterns):\n", "    # 创建滑动窗口\n", "    def value_on_size(size_of_pattern):\n", "        # 水平窗口\n", "        window_1 = np.lib.stride_tricks.sliding_window_view(board,size_of_pattern,axis=1)\n", "        print(f'{size_of_pattern}size 水平窗口:{window_1}\\n')\n", "        # 垂直窗口\n", "        window_2 = np.lib.stride_tricks.sliding_window_view(board,size_of_pattern,axis=0)\n", "        print(f'{size_of_pattern}size 竖直窗口:{window_2}\\n')\n", "        # 斜窗口\n", "        diagonal_board = [\n", "            np.lib.stride_tricks.sliding_window_view(\n", "                np.diagonal(board,offset=k),size_of_pattern) \n", "                for k in range(-board.shape[0] + size_of_pattern,board.shape[0] - size_of_pattern + 1)]\n", "        diagonal_board += [\n", "                np.lib.stride_tricks.sliding_window_view(\n", "                    np.diagonal(np.fliplr(board),offset=k),size_of_pattern) \n", "                    for k in range(-board.shape[0] + size_of_pattern,board.shape[0] - size_of_pattern + 1)]\n", "        window_3 = np.concatenate(diagonal_board,axis=0)\n", "        print(f'{size_of_pattern}size 斜窗口:{window_2}\\n')\n", "        window = np.concatenate((window_1.reshape(-1,size_of_pattern),window_2.reshape(-1,size_of_pattern),window_3.reshape(-1,size_of_pattern)),axis=0)\n", "        print(f'{size_of_pattern}size 总窗口:{window_2}\\n')\n", "        sum_value = 0\n", "        for pattern in patterns[size_of_pattern - 4]:\n", "            sum_value += np.sum(np.all(window == pattern[0],axis=-1)) * pattern[1]\n", "            print(f'检测到{np.sum(np.all(window == pattern[0],axis = -1))}个{pattern[0]},每个价值{pattern[1]},目前总计价值{sum_value}\\n')\n", "        return sum_value\n", "    return value_on_size(4) + value_on_size(5) + value_on_size(6)"]}, {"cell_type": "code", "execution_count": 24, "id": "9811ab17", "metadata": {}, "outputs": [], "source": ["a = 1\n", "b = 2\n", "patterns = [\n", "            [\n", "            # 活二 (两端空)\n", "            (np.array([0, a, a, 0]), 100),\n", "            (np.array([0, b, b, 0]), -100),\n", "            # 眠二 (一端被堵)\n", "            (np.array([0, a, a, b]), 10),\n", "            (np.array([b, a, a, 0]), 10),\n", "            (np.array([0, b, b, a]), -10),\n", "            (np.array([a, b, b, 0]), -10),\n", "\n", "            ],\n", "            [\n", "            # 连五 (100000分)\n", "            (np.array([a, a, a, a, a]), 100000),\n", "            (np.array([b, b, b, b, b]), -100010),\n", "            # 活三（2000分）\n", "            (np.array([0, a, a, a, 0]), 2000),\n", "            (np.array([0, b, b, b, 0]), -2010),\n", "\n", "            # 眠三（500分）\n", "            (np.array([b, a, a, a, 0]), 500),\n", "            (np.array([0, a, a, a, b]), 500),\n", "            (np.array([a, b, b, b, 0]), -510),\n", "            (np.array([0, b, b, b, a]), -510) \n", "        ],\n", "        # 6*1patterns\n", "        [\n", "            # 活四\n", "            (np.array([0, a, a, a, a, 0]), 10000),\n", "            (np.array([0, b, b, b, b, 0]), -10010),\n", "            # 冲四\n", "            (np.array([0, a, a, a, a, b]), 5000),\n", "            (np.array([b, a, a, a, a, 0]), 5000),\n", "            (np.array([0, b, b, b, b, a]), -5010),\n", "            (np.array([a, b, b, b, b, 0]), -5010),\n", "        ]]"]}, {"cell_type": "code", "execution_count": 25, "id": "97ab705f", "metadata": {}, "outputs": [], "source": ["board = np.zeros((11,11))\n", "board[3][6] ,board[4][6],board[5][6] = 2 ,2 ,2\n", "board[4,4] ,board[5,4], board[6,4], board[5][5] = 1,1,1,1"]}, {"cell_type": "code", "execution_count": 26, "id": "863fa3fc", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["4size 水平窗口:[[[0. 0. 0. 0.]\n", "  [0. 0. 0. 0.]\n", "  [0. 0. 0. 0.]\n", "  [0. 0. 0. 0.]\n", "  [0. 0. 0. 0.]\n", "  [0. 0. 0. 0.]\n", "  [0. 0. 0. 0.]\n", "  [0. 0. 0. 0.]]\n", "\n", " [[0. 0. 0. 0.]\n", "  [0. 0. 0. 0.]\n", "  [0. 0. 0. 0.]\n", "  [0. 0. 0. 0.]\n", "  [0. 0. 0. 0.]\n", "  [0. 0. 0. 0.]\n", "  [0. 0. 0. 0.]\n", "  [0. 0. 0. 0.]]\n", "\n", " [[0. 0. 0. 0.]\n", "  [0. 0. 0. 0.]\n", "  [0. 0. 0. 0.]\n", "  [0. 0. 0. 0.]\n", "  [0. 0. 0. 0.]\n", "  [0. 0. 0. 0.]\n", "  [0. 0. 0. 0.]\n", "  [0. 0. 0. 0.]]\n", "\n", " [[0. 0. 0. 0.]\n", "  [0. 0. 0. 0.]\n", "  [0. 0. 0. 0.]\n", "  [0. 0. 0. 2.]\n", "  [0. 0. 2. 0.]\n", "  [0. 2. 0. 0.]\n", "  [2. 0. 0. 0.]\n", "  [0. 0. 0. 0.]]\n", "\n", " [[0. 0. 0. 0.]\n", "  [0. 0. 0. 1.]\n", "  [0. 0. 1. 0.]\n", "  [0. 1. 0. 2.]\n", "  [1. 0. 2. 0.]\n", "  [0. 2. 0. 0.]\n", "  [2. 0. 0. 0.]\n", "  [0. 0. 0. 0.]]\n", "\n", " [[0. 0. 0. 0.]\n", "  [0. 0. 0. 1.]\n", "  [0. 0. 1. 1.]\n", "  [0. 1. 1. 2.]\n", "  [1. 1. 2. 0.]\n", "  [1. 2. 0. 0.]\n", "  [2. 0. 0. 0.]\n", "  [0. 0. 0. 0.]]\n", "\n", " [[0. 0. 0. 0.]\n", "  [0. 0. 0. 1.]\n", "  [0. 0. 1. 0.]\n", "  [0. 1. 0. 0.]\n", "  [1. 0. 0. 0.]\n", "  [0. 0. 0. 0.]\n", "  [0. 0. 0. 0.]\n", "  [0. 0. 0. 0.]]\n", "\n", " [[0. 0. 0. 0.]\n", "  [0. 0. 0. 0.]\n", "  [0. 0. 0. 0.]\n", "  [0. 0. 0. 0.]\n", "  [0. 0. 0. 0.]\n", "  [0. 0. 0. 0.]\n", "  [0. 0. 0. 0.]\n", "  [0. 0. 0. 0.]]\n", "\n", " [[0. 0. 0. 0.]\n", "  [0. 0. 0. 0.]\n", "  [0. 0. 0. 0.]\n", "  [0. 0. 0. 0.]\n", "  [0. 0. 0. 0.]\n", "  [0. 0. 0. 0.]\n", "  [0. 0. 0. 0.]\n", "  [0. 0. 0. 0.]]\n", "\n", " [[0. 0. 0. 0.]\n", "  [0. 0. 0. 0.]\n", "  [0. 0. 0. 0.]\n", "  [0. 0. 0. 0.]\n", "  [0. 0. 0. 0.]\n", "  [0. 0. 0. 0.]\n", "  [0. 0. 0. 0.]\n", "  [0. 0. 0. 0.]]\n", "\n", " [[0. 0. 0. 0.]\n", "  [0. 0. 0. 0.]\n", "  [0. 0. 0. 0.]\n", "  [0. 0. 0. 0.]\n", "  [0. 0. 0. 0.]\n", "  [0. 0. 0. 0.]\n", "  [0. 0. 0. 0.]\n", "  [0. 0. 0. 0.]]]\n", "\n", "4size 竖直窗口:[[[0. 0. 0. 0.]\n", "  [0. 0. 0. 0.]\n", "  [0. 0. 0. 0.]\n", "  [0. 0. 0. 0.]\n", "  [0. 0. 0. 0.]\n", "  [0. 0. 0. 0.]\n", "  [0. 0. 0. 2.]\n", "  [0. 0. 0. 0.]\n", "  [0. 0. 0. 0.]\n", "  [0. 0. 0. 0.]\n", "  [0. 0. 0. 0.]]\n", "\n", " [[0. 0. 0. 0.]\n", "  [0. 0. 0. 0.]\n", "  [0. 0. 0. 0.]\n", "  [0. 0. 0. 0.]\n", "  [0. 0. 0. 1.]\n", "  [0. 0. 0. 0.]\n", "  [0. 0. 2. 2.]\n", "  [0. 0. 0. 0.]\n", "  [0. 0. 0. 0.]\n", "  [0. 0. 0. 0.]\n", "  [0. 0. 0. 0.]]\n", "\n", " [[0. 0. 0. 0.]\n", "  [0. 0. 0. 0.]\n", "  [0. 0. 0. 0.]\n", "  [0. 0. 0. 0.]\n", "  [0. 0. 1. 1.]\n", "  [0. 0. 0. 1.]\n", "  [0. 2. 2. 2.]\n", "  [0. 0. 0. 0.]\n", "  [0. 0. 0. 0.]\n", "  [0. 0. 0. 0.]\n", "  [0. 0. 0. 0.]]\n", "\n", " [[0. 0. 0. 0.]\n", "  [0. 0. 0. 0.]\n", "  [0. 0. 0. 0.]\n", "  [0. 0. 0. 0.]\n", "  [0. 1. 1. 1.]\n", "  [0. 0. 1. 0.]\n", "  [2. 2. 2. 0.]\n", "  [0. 0. 0. 0.]\n", "  [0. 0. 0. 0.]\n", "  [0. 0. 0. 0.]\n", "  [0. 0. 0. 0.]]\n", "\n", " [[0. 0. 0. 0.]\n", "  [0. 0. 0. 0.]\n", "  [0. 0. 0. 0.]\n", "  [0. 0. 0. 0.]\n", "  [1. 1. 1. 0.]\n", "  [0. 1. 0. 0.]\n", "  [2. 2. 0. 0.]\n", "  [0. 0. 0. 0.]\n", "  [0. 0. 0. 0.]\n", "  [0. 0. 0. 0.]\n", "  [0. 0. 0. 0.]]\n", "\n", " [[0. 0. 0. 0.]\n", "  [0. 0. 0. 0.]\n", "  [0. 0. 0. 0.]\n", "  [0. 0. 0. 0.]\n", "  [1. 1. 0. 0.]\n", "  [1. 0. 0. 0.]\n", "  [2. 0. 0. 0.]\n", "  [0. 0. 0. 0.]\n", "  [0. 0. 0. 0.]\n", "  [0. 0. 0. 0.]\n", "  [0. 0. 0. 0.]]\n", "\n", " [[0. 0. 0. 0.]\n", "  [0. 0. 0. 0.]\n", "  [0. 0. 0. 0.]\n", "  [0. 0. 0. 0.]\n", "  [1. 0. 0. 0.]\n", "  [0. 0. 0. 0.]\n", "  [0. 0. 0. 0.]\n", "  [0. 0. 0. 0.]\n", "  [0. 0. 0. 0.]\n", "  [0. 0. 0. 0.]\n", "  [0. 0. 0. 0.]]\n", "\n", " [[0. 0. 0. 0.]\n", "  [0. 0. 0. 0.]\n", "  [0. 0. 0. 0.]\n", "  [0. 0. 0. 0.]\n", "  [0. 0. 0. 0.]\n", "  [0. 0. 0. 0.]\n", "  [0. 0. 0. 0.]\n", "  [0. 0. 0. 0.]\n", "  [0. 0. 0. 0.]\n", "  [0. 0. 0. 0.]\n", "  [0. 0. 0. 0.]]]\n", "\n", "4size 斜窗口:[[[0. 0. 0. 0.]\n", "  [0. 0. 0. 0.]\n", "  [0. 0. 0. 0.]\n", "  [0. 0. 0. 0.]\n", "  [0. 0. 0. 0.]\n", "  [0. 0. 0. 0.]\n", "  [0. 0. 0. 2.]\n", "  [0. 0. 0. 0.]\n", "  [0. 0. 0. 0.]\n", "  [0. 0. 0. 0.]\n", "  [0. 0. 0. 0.]]\n", "\n", " [[0. 0. 0. 0.]\n", "  [0. 0. 0. 0.]\n", "  [0. 0. 0. 0.]\n", "  [0. 0. 0. 0.]\n", "  [0. 0. 0. 1.]\n", "  [0. 0. 0. 0.]\n", "  [0. 0. 2. 2.]\n", "  [0. 0. 0. 0.]\n", "  [0. 0. 0. 0.]\n", "  [0. 0. 0. 0.]\n", "  [0. 0. 0. 0.]]\n", "\n", " [[0. 0. 0. 0.]\n", "  [0. 0. 0. 0.]\n", "  [0. 0. 0. 0.]\n", "  [0. 0. 0. 0.]\n", "  [0. 0. 1. 1.]\n", "  [0. 0. 0. 1.]\n", "  [0. 2. 2. 2.]\n", "  [0. 0. 0. 0.]\n", "  [0. 0. 0. 0.]\n", "  [0. 0. 0. 0.]\n", "  [0. 0. 0. 0.]]\n", "\n", " [[0. 0. 0. 0.]\n", "  [0. 0. 0. 0.]\n", "  [0. 0. 0. 0.]\n", "  [0. 0. 0. 0.]\n", "  [0. 1. 1. 1.]\n", "  [0. 0. 1. 0.]\n", "  [2. 2. 2. 0.]\n", "  [0. 0. 0. 0.]\n", "  [0. 0. 0. 0.]\n", "  [0. 0. 0. 0.]\n", "  [0. 0. 0. 0.]]\n", "\n", " [[0. 0. 0. 0.]\n", "  [0. 0. 0. 0.]\n", "  [0. 0. 0. 0.]\n", "  [0. 0. 0. 0.]\n", "  [1. 1. 1. 0.]\n", "  [0. 1. 0. 0.]\n", "  [2. 2. 0. 0.]\n", "  [0. 0. 0. 0.]\n", "  [0. 0. 0. 0.]\n", "  [0. 0. 0. 0.]\n", "  [0. 0. 0. 0.]]\n", "\n", " [[0. 0. 0. 0.]\n", "  [0. 0. 0. 0.]\n", "  [0. 0. 0. 0.]\n", "  [0. 0. 0. 0.]\n", "  [1. 1. 0. 0.]\n", "  [1. 0. 0. 0.]\n", "  [2. 0. 0. 0.]\n", "  [0. 0. 0. 0.]\n", "  [0. 0. 0. 0.]\n", "  [0. 0. 0. 0.]\n", "  [0. 0. 0. 0.]]\n", "\n", " [[0. 0. 0. 0.]\n", "  [0. 0. 0. 0.]\n", "  [0. 0. 0. 0.]\n", "  [0. 0. 0. 0.]\n", "  [1. 0. 0. 0.]\n", "  [0. 0. 0. 0.]\n", "  [0. 0. 0. 0.]\n", "  [0. 0. 0. 0.]\n", "  [0. 0. 0. 0.]\n", "  [0. 0. 0. 0.]\n", "  [0. 0. 0. 0.]]\n", "\n", " [[0. 0. 0. 0.]\n", "  [0. 0. 0. 0.]\n", "  [0. 0. 0. 0.]\n", "  [0. 0. 0. 0.]\n", "  [0. 0. 0. 0.]\n", "  [0. 0. 0. 0.]\n", "  [0. 0. 0. 0.]\n", "  [0. 0. 0. 0.]\n", "  [0. 0. 0. 0.]\n", "  [0. 0. 0. 0.]\n", "  [0. 0. 0. 0.]]]\n", "\n", "4size 总窗口:[[[0. 0. 0. 0.]\n", "  [0. 0. 0. 0.]\n", "  [0. 0. 0. 0.]\n", "  [0. 0. 0. 0.]\n", "  [0. 0. 0. 0.]\n", "  [0. 0. 0. 0.]\n", "  [0. 0. 0. 2.]\n", "  [0. 0. 0. 0.]\n", "  [0. 0. 0. 0.]\n", "  [0. 0. 0. 0.]\n", "  [0. 0. 0. 0.]]\n", "\n", " [[0. 0. 0. 0.]\n", "  [0. 0. 0. 0.]\n", "  [0. 0. 0. 0.]\n", "  [0. 0. 0. 0.]\n", "  [0. 0. 0. 1.]\n", "  [0. 0. 0. 0.]\n", "  [0. 0. 2. 2.]\n", "  [0. 0. 0. 0.]\n", "  [0. 0. 0. 0.]\n", "  [0. 0. 0. 0.]\n", "  [0. 0. 0. 0.]]\n", "\n", " [[0. 0. 0. 0.]\n", "  [0. 0. 0. 0.]\n", "  [0. 0. 0. 0.]\n", "  [0. 0. 0. 0.]\n", "  [0. 0. 1. 1.]\n", "  [0. 0. 0. 1.]\n", "  [0. 2. 2. 2.]\n", "  [0. 0. 0. 0.]\n", "  [0. 0. 0. 0.]\n", "  [0. 0. 0. 0.]\n", "  [0. 0. 0. 0.]]\n", "\n", " [[0. 0. 0. 0.]\n", "  [0. 0. 0. 0.]\n", "  [0. 0. 0. 0.]\n", "  [0. 0. 0. 0.]\n", "  [0. 1. 1. 1.]\n", "  [0. 0. 1. 0.]\n", "  [2. 2. 2. 0.]\n", "  [0. 0. 0. 0.]\n", "  [0. 0. 0. 0.]\n", "  [0. 0. 0. 0.]\n", "  [0. 0. 0. 0.]]\n", "\n", " [[0. 0. 0. 0.]\n", "  [0. 0. 0. 0.]\n", "  [0. 0. 0. 0.]\n", "  [0. 0. 0. 0.]\n", "  [1. 1. 1. 0.]\n", "  [0. 1. 0. 0.]\n", "  [2. 2. 0. 0.]\n", "  [0. 0. 0. 0.]\n", "  [0. 0. 0. 0.]\n", "  [0. 0. 0. 0.]\n", "  [0. 0. 0. 0.]]\n", "\n", " [[0. 0. 0. 0.]\n", "  [0. 0. 0. 0.]\n", "  [0. 0. 0. 0.]\n", "  [0. 0. 0. 0.]\n", "  [1. 1. 0. 0.]\n", "  [1. 0. 0. 0.]\n", "  [2. 0. 0. 0.]\n", "  [0. 0. 0. 0.]\n", "  [0. 0. 0. 0.]\n", "  [0. 0. 0. 0.]\n", "  [0. 0. 0. 0.]]\n", "\n", " [[0. 0. 0. 0.]\n", "  [0. 0. 0. 0.]\n", "  [0. 0. 0. 0.]\n", "  [0. 0. 0. 0.]\n", "  [1. 0. 0. 0.]\n", "  [0. 0. 0. 0.]\n", "  [0. 0. 0. 0.]\n", "  [0. 0. 0. 0.]\n", "  [0. 0. 0. 0.]\n", "  [0. 0. 0. 0.]\n", "  [0. 0. 0. 0.]]\n", "\n", " [[0. 0. 0. 0.]\n", "  [0. 0. 0. 0.]\n", "  [0. 0. 0. 0.]\n", "  [0. 0. 0. 0.]\n", "  [0. 0. 0. 0.]\n", "  [0. 0. 0. 0.]\n", "  [0. 0. 0. 0.]\n", "  [0. 0. 0. 0.]\n", "  [0. 0. 0. 0.]\n", "  [0. 0. 0. 0.]\n", "  [0. 0. 0. 0.]]]\n", "\n", "检测到1个[0 1 1 0],每个价值100,目前总计价值100\n", "\n", "检测到0个[0 2 2 0],每个价值-100,目前总计价值100\n", "\n", "检测到1个[0 1 1 2],每个价值10,目前总计价值110\n", "\n", "检测到1个[2 1 1 0],每个价值10,目前总计价值120\n", "\n", "检测到0个[0 2 2 1],每个价值-10,目前总计价值120\n", "\n", "检测到0个[1 2 2 0],每个价值-10,目前总计价值120\n", "\n", "5size 水平窗口:[[[0. 0. 0. 0. 0.]\n", "  [0. 0. 0. 0. 0.]\n", "  [0. 0. 0. 0. 0.]\n", "  [0. 0. 0. 0. 0.]\n", "  [0. 0. 0. 0. 0.]\n", "  [0. 0. 0. 0. 0.]\n", "  [0. 0. 0. 0. 0.]]\n", "\n", " [[0. 0. 0. 0. 0.]\n", "  [0. 0. 0. 0. 0.]\n", "  [0. 0. 0. 0. 0.]\n", "  [0. 0. 0. 0. 0.]\n", "  [0. 0. 0. 0. 0.]\n", "  [0. 0. 0. 0. 0.]\n", "  [0. 0. 0. 0. 0.]]\n", "\n", " [[0. 0. 0. 0. 0.]\n", "  [0. 0. 0. 0. 0.]\n", "  [0. 0. 0. 0. 0.]\n", "  [0. 0. 0. 0. 0.]\n", "  [0. 0. 0. 0. 0.]\n", "  [0. 0. 0. 0. 0.]\n", "  [0. 0. 0. 0. 0.]]\n", "\n", " [[0. 0. 0. 0. 0.]\n", "  [0. 0. 0. 0. 0.]\n", "  [0. 0. 0. 0. 2.]\n", "  [0. 0. 0. 2. 0.]\n", "  [0. 0. 2. 0. 0.]\n", "  [0. 2. 0. 0. 0.]\n", "  [2. 0. 0. 0. 0.]]\n", "\n", " [[0. 0. 0. 0. 1.]\n", "  [0. 0. 0. 1. 0.]\n", "  [0. 0. 1. 0. 2.]\n", "  [0. 1. 0. 2. 0.]\n", "  [1. 0. 2. 0. 0.]\n", "  [0. 2. 0. 0. 0.]\n", "  [2. 0. 0. 0. 0.]]\n", "\n", " [[0. 0. 0. 0. 1.]\n", "  [0. 0. 0. 1. 1.]\n", "  [0. 0. 1. 1. 2.]\n", "  [0. 1. 1. 2. 0.]\n", "  [1. 1. 2. 0. 0.]\n", "  [1. 2. 0. 0. 0.]\n", "  [2. 0. 0. 0. 0.]]\n", "\n", " [[0. 0. 0. 0. 1.]\n", "  [0. 0. 0. 1. 0.]\n", "  [0. 0. 1. 0. 0.]\n", "  [0. 1. 0. 0. 0.]\n", "  [1. 0. 0. 0. 0.]\n", "  [0. 0. 0. 0. 0.]\n", "  [0. 0. 0. 0. 0.]]\n", "\n", " [[0. 0. 0. 0. 0.]\n", "  [0. 0. 0. 0. 0.]\n", "  [0. 0. 0. 0. 0.]\n", "  [0. 0. 0. 0. 0.]\n", "  [0. 0. 0. 0. 0.]\n", "  [0. 0. 0. 0. 0.]\n", "  [0. 0. 0. 0. 0.]]\n", "\n", " [[0. 0. 0. 0. 0.]\n", "  [0. 0. 0. 0. 0.]\n", "  [0. 0. 0. 0. 0.]\n", "  [0. 0. 0. 0. 0.]\n", "  [0. 0. 0. 0. 0.]\n", "  [0. 0. 0. 0. 0.]\n", "  [0. 0. 0. 0. 0.]]\n", "\n", " [[0. 0. 0. 0. 0.]\n", "  [0. 0. 0. 0. 0.]\n", "  [0. 0. 0. 0. 0.]\n", "  [0. 0. 0. 0. 0.]\n", "  [0. 0. 0. 0. 0.]\n", "  [0. 0. 0. 0. 0.]\n", "  [0. 0. 0. 0. 0.]]\n", "\n", " [[0. 0. 0. 0. 0.]\n", "  [0. 0. 0. 0. 0.]\n", "  [0. 0. 0. 0. 0.]\n", "  [0. 0. 0. 0. 0.]\n", "  [0. 0. 0. 0. 0.]\n", "  [0. 0. 0. 0. 0.]\n", "  [0. 0. 0. 0. 0.]]]\n", "\n", "5size 竖直窗口:[[[0. 0. 0. 0. 0.]\n", "  [0. 0. 0. 0. 0.]\n", "  [0. 0. 0. 0. 0.]\n", "  [0. 0. 0. 0. 0.]\n", "  [0. 0. 0. 0. 1.]\n", "  [0. 0. 0. 0. 0.]\n", "  [0. 0. 0. 2. 2.]\n", "  [0. 0. 0. 0. 0.]\n", "  [0. 0. 0. 0. 0.]\n", "  [0. 0. 0. 0. 0.]\n", "  [0. 0. 0. 0. 0.]]\n", "\n", " [[0. 0. 0. 0. 0.]\n", "  [0. 0. 0. 0. 0.]\n", "  [0. 0. 0. 0. 0.]\n", "  [0. 0. 0. 0. 0.]\n", "  [0. 0. 0. 1. 1.]\n", "  [0. 0. 0. 0. 1.]\n", "  [0. 0. 2. 2. 2.]\n", "  [0. 0. 0. 0. 0.]\n", "  [0. 0. 0. 0. 0.]\n", "  [0. 0. 0. 0. 0.]\n", "  [0. 0. 0. 0. 0.]]\n", "\n", " [[0. 0. 0. 0. 0.]\n", "  [0. 0. 0. 0. 0.]\n", "  [0. 0. 0. 0. 0.]\n", "  [0. 0. 0. 0. 0.]\n", "  [0. 0. 1. 1. 1.]\n", "  [0. 0. 0. 1. 0.]\n", "  [0. 2. 2. 2. 0.]\n", "  [0. 0. 0. 0. 0.]\n", "  [0. 0. 0. 0. 0.]\n", "  [0. 0. 0. 0. 0.]\n", "  [0. 0. 0. 0. 0.]]\n", "\n", " [[0. 0. 0. 0. 0.]\n", "  [0. 0. 0. 0. 0.]\n", "  [0. 0. 0. 0. 0.]\n", "  [0. 0. 0. 0. 0.]\n", "  [0. 1. 1. 1. 0.]\n", "  [0. 0. 1. 0. 0.]\n", "  [2. 2. 2. 0. 0.]\n", "  [0. 0. 0. 0. 0.]\n", "  [0. 0. 0. 0. 0.]\n", "  [0. 0. 0. 0. 0.]\n", "  [0. 0. 0. 0. 0.]]\n", "\n", " [[0. 0. 0. 0. 0.]\n", "  [0. 0. 0. 0. 0.]\n", "  [0. 0. 0. 0. 0.]\n", "  [0. 0. 0. 0. 0.]\n", "  [1. 1. 1. 0. 0.]\n", "  [0. 1. 0. 0. 0.]\n", "  [2. 2. 0. 0. 0.]\n", "  [0. 0. 0. 0. 0.]\n", "  [0. 0. 0. 0. 0.]\n", "  [0. 0. 0. 0. 0.]\n", "  [0. 0. 0. 0. 0.]]\n", "\n", " [[0. 0. 0. 0. 0.]\n", "  [0. 0. 0. 0. 0.]\n", "  [0. 0. 0. 0. 0.]\n", "  [0. 0. 0. 0. 0.]\n", "  [1. 1. 0. 0. 0.]\n", "  [1. 0. 0. 0. 0.]\n", "  [2. 0. 0. 0. 0.]\n", "  [0. 0. 0. 0. 0.]\n", "  [0. 0. 0. 0. 0.]\n", "  [0. 0. 0. 0. 0.]\n", "  [0. 0. 0. 0. 0.]]\n", "\n", " [[0. 0. 0. 0. 0.]\n", "  [0. 0. 0. 0. 0.]\n", "  [0. 0. 0. 0. 0.]\n", "  [0. 0. 0. 0. 0.]\n", "  [1. 0. 0. 0. 0.]\n", "  [0. 0. 0. 0. 0.]\n", "  [0. 0. 0. 0. 0.]\n", "  [0. 0. 0. 0. 0.]\n", "  [0. 0. 0. 0. 0.]\n", "  [0. 0. 0. 0. 0.]\n", "  [0. 0. 0. 0. 0.]]]\n", "\n", "5size 斜窗口:[[[0. 0. 0. 0. 0.]\n", "  [0. 0. 0. 0. 0.]\n", "  [0. 0. 0. 0. 0.]\n", "  [0. 0. 0. 0. 0.]\n", "  [0. 0. 0. 0. 1.]\n", "  [0. 0. 0. 0. 0.]\n", "  [0. 0. 0. 2. 2.]\n", "  [0. 0. 0. 0. 0.]\n", "  [0. 0. 0. 0. 0.]\n", "  [0. 0. 0. 0. 0.]\n", "  [0. 0. 0. 0. 0.]]\n", "\n", " [[0. 0. 0. 0. 0.]\n", "  [0. 0. 0. 0. 0.]\n", "  [0. 0. 0. 0. 0.]\n", "  [0. 0. 0. 0. 0.]\n", "  [0. 0. 0. 1. 1.]\n", "  [0. 0. 0. 0. 1.]\n", "  [0. 0. 2. 2. 2.]\n", "  [0. 0. 0. 0. 0.]\n", "  [0. 0. 0. 0. 0.]\n", "  [0. 0. 0. 0. 0.]\n", "  [0. 0. 0. 0. 0.]]\n", "\n", " [[0. 0. 0. 0. 0.]\n", "  [0. 0. 0. 0. 0.]\n", "  [0. 0. 0. 0. 0.]\n", "  [0. 0. 0. 0. 0.]\n", "  [0. 0. 1. 1. 1.]\n", "  [0. 0. 0. 1. 0.]\n", "  [0. 2. 2. 2. 0.]\n", "  [0. 0. 0. 0. 0.]\n", "  [0. 0. 0. 0. 0.]\n", "  [0. 0. 0. 0. 0.]\n", "  [0. 0. 0. 0. 0.]]\n", "\n", " [[0. 0. 0. 0. 0.]\n", "  [0. 0. 0. 0. 0.]\n", "  [0. 0. 0. 0. 0.]\n", "  [0. 0. 0. 0. 0.]\n", "  [0. 1. 1. 1. 0.]\n", "  [0. 0. 1. 0. 0.]\n", "  [2. 2. 2. 0. 0.]\n", "  [0. 0. 0. 0. 0.]\n", "  [0. 0. 0. 0. 0.]\n", "  [0. 0. 0. 0. 0.]\n", "  [0. 0. 0. 0. 0.]]\n", "\n", " [[0. 0. 0. 0. 0.]\n", "  [0. 0. 0. 0. 0.]\n", "  [0. 0. 0. 0. 0.]\n", "  [0. 0. 0. 0. 0.]\n", "  [1. 1. 1. 0. 0.]\n", "  [0. 1. 0. 0. 0.]\n", "  [2. 2. 0. 0. 0.]\n", "  [0. 0. 0. 0. 0.]\n", "  [0. 0. 0. 0. 0.]\n", "  [0. 0. 0. 0. 0.]\n", "  [0. 0. 0. 0. 0.]]\n", "\n", " [[0. 0. 0. 0. 0.]\n", "  [0. 0. 0. 0. 0.]\n", "  [0. 0. 0. 0. 0.]\n", "  [0. 0. 0. 0. 0.]\n", "  [1. 1. 0. 0. 0.]\n", "  [1. 0. 0. 0. 0.]\n", "  [2. 0. 0. 0. 0.]\n", "  [0. 0. 0. 0. 0.]\n", "  [0. 0. 0. 0. 0.]\n", "  [0. 0. 0. 0. 0.]\n", "  [0. 0. 0. 0. 0.]]\n", "\n", " [[0. 0. 0. 0. 0.]\n", "  [0. 0. 0. 0. 0.]\n", "  [0. 0. 0. 0. 0.]\n", "  [0. 0. 0. 0. 0.]\n", "  [1. 0. 0. 0. 0.]\n", "  [0. 0. 0. 0. 0.]\n", "  [0. 0. 0. 0. 0.]\n", "  [0. 0. 0. 0. 0.]\n", "  [0. 0. 0. 0. 0.]\n", "  [0. 0. 0. 0. 0.]\n", "  [0. 0. 0. 0. 0.]]]\n", "\n", "5size 总窗口:[[[0. 0. 0. 0. 0.]\n", "  [0. 0. 0. 0. 0.]\n", "  [0. 0. 0. 0. 0.]\n", "  [0. 0. 0. 0. 0.]\n", "  [0. 0. 0. 0. 1.]\n", "  [0. 0. 0. 0. 0.]\n", "  [0. 0. 0. 2. 2.]\n", "  [0. 0. 0. 0. 0.]\n", "  [0. 0. 0. 0. 0.]\n", "  [0. 0. 0. 0. 0.]\n", "  [0. 0. 0. 0. 0.]]\n", "\n", " [[0. 0. 0. 0. 0.]\n", "  [0. 0. 0. 0. 0.]\n", "  [0. 0. 0. 0. 0.]\n", "  [0. 0. 0. 0. 0.]\n", "  [0. 0. 0. 1. 1.]\n", "  [0. 0. 0. 0. 1.]\n", "  [0. 0. 2. 2. 2.]\n", "  [0. 0. 0. 0. 0.]\n", "  [0. 0. 0. 0. 0.]\n", "  [0. 0. 0. 0. 0.]\n", "  [0. 0. 0. 0. 0.]]\n", "\n", " [[0. 0. 0. 0. 0.]\n", "  [0. 0. 0. 0. 0.]\n", "  [0. 0. 0. 0. 0.]\n", "  [0. 0. 0. 0. 0.]\n", "  [0. 0. 1. 1. 1.]\n", "  [0. 0. 0. 1. 0.]\n", "  [0. 2. 2. 2. 0.]\n", "  [0. 0. 0. 0. 0.]\n", "  [0. 0. 0. 0. 0.]\n", "  [0. 0. 0. 0. 0.]\n", "  [0. 0. 0. 0. 0.]]\n", "\n", " [[0. 0. 0. 0. 0.]\n", "  [0. 0. 0. 0. 0.]\n", "  [0. 0. 0. 0. 0.]\n", "  [0. 0. 0. 0. 0.]\n", "  [0. 1. 1. 1. 0.]\n", "  [0. 0. 1. 0. 0.]\n", "  [2. 2. 2. 0. 0.]\n", "  [0. 0. 0. 0. 0.]\n", "  [0. 0. 0. 0. 0.]\n", "  [0. 0. 0. 0. 0.]\n", "  [0. 0. 0. 0. 0.]]\n", "\n", " [[0. 0. 0. 0. 0.]\n", "  [0. 0. 0. 0. 0.]\n", "  [0. 0. 0. 0. 0.]\n", "  [0. 0. 0. 0. 0.]\n", "  [1. 1. 1. 0. 0.]\n", "  [0. 1. 0. 0. 0.]\n", "  [2. 2. 0. 0. 0.]\n", "  [0. 0. 0. 0. 0.]\n", "  [0. 0. 0. 0. 0.]\n", "  [0. 0. 0. 0. 0.]\n", "  [0. 0. 0. 0. 0.]]\n", "\n", " [[0. 0. 0. 0. 0.]\n", "  [0. 0. 0. 0. 0.]\n", "  [0. 0. 0. 0. 0.]\n", "  [0. 0. 0. 0. 0.]\n", "  [1. 1. 0. 0. 0.]\n", "  [1. 0. 0. 0. 0.]\n", "  [2. 0. 0. 0. 0.]\n", "  [0. 0. 0. 0. 0.]\n", "  [0. 0. 0. 0. 0.]\n", "  [0. 0. 0. 0. 0.]\n", "  [0. 0. 0. 0. 0.]]\n", "\n", " [[0. 0. 0. 0. 0.]\n", "  [0. 0. 0. 0. 0.]\n", "  [0. 0. 0. 0. 0.]\n", "  [0. 0. 0. 0. 0.]\n", "  [1. 0. 0. 0. 0.]\n", "  [0. 0. 0. 0. 0.]\n", "  [0. 0. 0. 0. 0.]\n", "  [0. 0. 0. 0. 0.]\n", "  [0. 0. 0. 0. 0.]\n", "  [0. 0. 0. 0. 0.]\n", "  [0. 0. 0. 0. 0.]]]\n", "\n", "检测到0个[1 1 1 1 1],每个价值100000,目前总计价值0\n", "\n", "检测到0个[2 2 2 2 2],每个价值-100010,目前总计价值0\n", "\n", "检测到1个[0 1 1 1 0],每个价值2000,目前总计价值2000\n", "\n", "检测到1个[0 2 2 2 0],每个价值-2010,目前总计价值-10\n", "\n", "检测到0个[2 1 1 1 0],每个价值500,目前总计价值-10\n", "\n", "检测到0个[0 1 1 1 2],每个价值500,目前总计价值-10\n", "\n", "检测到0个[1 2 2 2 0],每个价值-510,目前总计价值-10\n", "\n", "检测到0个[0 2 2 2 1],每个价值-510,目前总计价值-10\n", "\n", "6size 水平窗口:[[[0. 0. 0. 0. 0. 0.]\n", "  [0. 0. 0. 0. 0. 0.]\n", "  [0. 0. 0. 0. 0. 0.]\n", "  [0. 0. 0. 0. 0. 0.]\n", "  [0. 0. 0. 0. 0. 0.]\n", "  [0. 0. 0. 0. 0. 0.]]\n", "\n", " [[0. 0. 0. 0. 0. 0.]\n", "  [0. 0. 0. 0. 0. 0.]\n", "  [0. 0. 0. 0. 0. 0.]\n", "  [0. 0. 0. 0. 0. 0.]\n", "  [0. 0. 0. 0. 0. 0.]\n", "  [0. 0. 0. 0. 0. 0.]]\n", "\n", " [[0. 0. 0. 0. 0. 0.]\n", "  [0. 0. 0. 0. 0. 0.]\n", "  [0. 0. 0. 0. 0. 0.]\n", "  [0. 0. 0. 0. 0. 0.]\n", "  [0. 0. 0. 0. 0. 0.]\n", "  [0. 0. 0. 0. 0. 0.]]\n", "\n", " [[0. 0. 0. 0. 0. 0.]\n", "  [0. 0. 0. 0. 0. 2.]\n", "  [0. 0. 0. 0. 2. 0.]\n", "  [0. 0. 0. 2. 0. 0.]\n", "  [0. 0. 2. 0. 0. 0.]\n", "  [0. 2. 0. 0. 0. 0.]]\n", "\n", " [[0. 0. 0. 0. 1. 0.]\n", "  [0. 0. 0. 1. 0. 2.]\n", "  [0. 0. 1. 0. 2. 0.]\n", "  [0. 1. 0. 2. 0. 0.]\n", "  [1. 0. 2. 0. 0. 0.]\n", "  [0. 2. 0. 0. 0. 0.]]\n", "\n", " [[0. 0. 0. 0. 1. 1.]\n", "  [0. 0. 0. 1. 1. 2.]\n", "  [0. 0. 1. 1. 2. 0.]\n", "  [0. 1. 1. 2. 0. 0.]\n", "  [1. 1. 2. 0. 0. 0.]\n", "  [1. 2. 0. 0. 0. 0.]]\n", "\n", " [[0. 0. 0. 0. 1. 0.]\n", "  [0. 0. 0. 1. 0. 0.]\n", "  [0. 0. 1. 0. 0. 0.]\n", "  [0. 1. 0. 0. 0. 0.]\n", "  [1. 0. 0. 0. 0. 0.]\n", "  [0. 0. 0. 0. 0. 0.]]\n", "\n", " [[0. 0. 0. 0. 0. 0.]\n", "  [0. 0. 0. 0. 0. 0.]\n", "  [0. 0. 0. 0. 0. 0.]\n", "  [0. 0. 0. 0. 0. 0.]\n", "  [0. 0. 0. 0. 0. 0.]\n", "  [0. 0. 0. 0. 0. 0.]]\n", "\n", " [[0. 0. 0. 0. 0. 0.]\n", "  [0. 0. 0. 0. 0. 0.]\n", "  [0. 0. 0. 0. 0. 0.]\n", "  [0. 0. 0. 0. 0. 0.]\n", "  [0. 0. 0. 0. 0. 0.]\n", "  [0. 0. 0. 0. 0. 0.]]\n", "\n", " [[0. 0. 0. 0. 0. 0.]\n", "  [0. 0. 0. 0. 0. 0.]\n", "  [0. 0. 0. 0. 0. 0.]\n", "  [0. 0. 0. 0. 0. 0.]\n", "  [0. 0. 0. 0. 0. 0.]\n", "  [0. 0. 0. 0. 0. 0.]]\n", "\n", " [[0. 0. 0. 0. 0. 0.]\n", "  [0. 0. 0. 0. 0. 0.]\n", "  [0. 0. 0. 0. 0. 0.]\n", "  [0. 0. 0. 0. 0. 0.]\n", "  [0. 0. 0. 0. 0. 0.]\n", "  [0. 0. 0. 0. 0. 0.]]]\n", "\n", "6size 竖直窗口:[[[0. 0. 0. 0. 0. 0.]\n", "  [0. 0. 0. 0. 0. 0.]\n", "  [0. 0. 0. 0. 0. 0.]\n", "  [0. 0. 0. 0. 0. 0.]\n", "  [0. 0. 0. 0. 1. 1.]\n", "  [0. 0. 0. 0. 0. 1.]\n", "  [0. 0. 0. 2. 2. 2.]\n", "  [0. 0. 0. 0. 0. 0.]\n", "  [0. 0. 0. 0. 0. 0.]\n", "  [0. 0. 0. 0. 0. 0.]\n", "  [0. 0. 0. 0. 0. 0.]]\n", "\n", " [[0. 0. 0. 0. 0. 0.]\n", "  [0. 0. 0. 0. 0. 0.]\n", "  [0. 0. 0. 0. 0. 0.]\n", "  [0. 0. 0. 0. 0. 0.]\n", "  [0. 0. 0. 1. 1. 1.]\n", "  [0. 0. 0. 0. 1. 0.]\n", "  [0. 0. 2. 2. 2. 0.]\n", "  [0. 0. 0. 0. 0. 0.]\n", "  [0. 0. 0. 0. 0. 0.]\n", "  [0. 0. 0. 0. 0. 0.]\n", "  [0. 0. 0. 0. 0. 0.]]\n", "\n", " [[0. 0. 0. 0. 0. 0.]\n", "  [0. 0. 0. 0. 0. 0.]\n", "  [0. 0. 0. 0. 0. 0.]\n", "  [0. 0. 0. 0. 0. 0.]\n", "  [0. 0. 1. 1. 1. 0.]\n", "  [0. 0. 0. 1. 0. 0.]\n", "  [0. 2. 2. 2. 0. 0.]\n", "  [0. 0. 0. 0. 0. 0.]\n", "  [0. 0. 0. 0. 0. 0.]\n", "  [0. 0. 0. 0. 0. 0.]\n", "  [0. 0. 0. 0. 0. 0.]]\n", "\n", " [[0. 0. 0. 0. 0. 0.]\n", "  [0. 0. 0. 0. 0. 0.]\n", "  [0. 0. 0. 0. 0. 0.]\n", "  [0. 0. 0. 0. 0. 0.]\n", "  [0. 1. 1. 1. 0. 0.]\n", "  [0. 0. 1. 0. 0. 0.]\n", "  [2. 2. 2. 0. 0. 0.]\n", "  [0. 0. 0. 0. 0. 0.]\n", "  [0. 0. 0. 0. 0. 0.]\n", "  [0. 0. 0. 0. 0. 0.]\n", "  [0. 0. 0. 0. 0. 0.]]\n", "\n", " [[0. 0. 0. 0. 0. 0.]\n", "  [0. 0. 0. 0. 0. 0.]\n", "  [0. 0. 0. 0. 0. 0.]\n", "  [0. 0. 0. 0. 0. 0.]\n", "  [1. 1. 1. 0. 0. 0.]\n", "  [0. 1. 0. 0. 0. 0.]\n", "  [2. 2. 0. 0. 0. 0.]\n", "  [0. 0. 0. 0. 0. 0.]\n", "  [0. 0. 0. 0. 0. 0.]\n", "  [0. 0. 0. 0. 0. 0.]\n", "  [0. 0. 0. 0. 0. 0.]]\n", "\n", " [[0. 0. 0. 0. 0. 0.]\n", "  [0. 0. 0. 0. 0. 0.]\n", "  [0. 0. 0. 0. 0. 0.]\n", "  [0. 0. 0. 0. 0. 0.]\n", "  [1. 1. 0. 0. 0. 0.]\n", "  [1. 0. 0. 0. 0. 0.]\n", "  [2. 0. 0. 0. 0. 0.]\n", "  [0. 0. 0. 0. 0. 0.]\n", "  [0. 0. 0. 0. 0. 0.]\n", "  [0. 0. 0. 0. 0. 0.]\n", "  [0. 0. 0. 0. 0. 0.]]]\n", "\n", "6size 斜窗口:[[[0. 0. 0. 0. 0. 0.]\n", "  [0. 0. 0. 0. 0. 0.]\n", "  [0. 0. 0. 0. 0. 0.]\n", "  [0. 0. 0. 0. 0. 0.]\n", "  [0. 0. 0. 0. 1. 1.]\n", "  [0. 0. 0. 0. 0. 1.]\n", "  [0. 0. 0. 2. 2. 2.]\n", "  [0. 0. 0. 0. 0. 0.]\n", "  [0. 0. 0. 0. 0. 0.]\n", "  [0. 0. 0. 0. 0. 0.]\n", "  [0. 0. 0. 0. 0. 0.]]\n", "\n", " [[0. 0. 0. 0. 0. 0.]\n", "  [0. 0. 0. 0. 0. 0.]\n", "  [0. 0. 0. 0. 0. 0.]\n", "  [0. 0. 0. 0. 0. 0.]\n", "  [0. 0. 0. 1. 1. 1.]\n", "  [0. 0. 0. 0. 1. 0.]\n", "  [0. 0. 2. 2. 2. 0.]\n", "  [0. 0. 0. 0. 0. 0.]\n", "  [0. 0. 0. 0. 0. 0.]\n", "  [0. 0. 0. 0. 0. 0.]\n", "  [0. 0. 0. 0. 0. 0.]]\n", "\n", " [[0. 0. 0. 0. 0. 0.]\n", "  [0. 0. 0. 0. 0. 0.]\n", "  [0. 0. 0. 0. 0. 0.]\n", "  [0. 0. 0. 0. 0. 0.]\n", "  [0. 0. 1. 1. 1. 0.]\n", "  [0. 0. 0. 1. 0. 0.]\n", "  [0. 2. 2. 2. 0. 0.]\n", "  [0. 0. 0. 0. 0. 0.]\n", "  [0. 0. 0. 0. 0. 0.]\n", "  [0. 0. 0. 0. 0. 0.]\n", "  [0. 0. 0. 0. 0. 0.]]\n", "\n", " [[0. 0. 0. 0. 0. 0.]\n", "  [0. 0. 0. 0. 0. 0.]\n", "  [0. 0. 0. 0. 0. 0.]\n", "  [0. 0. 0. 0. 0. 0.]\n", "  [0. 1. 1. 1. 0. 0.]\n", "  [0. 0. 1. 0. 0. 0.]\n", "  [2. 2. 2. 0. 0. 0.]\n", "  [0. 0. 0. 0. 0. 0.]\n", "  [0. 0. 0. 0. 0. 0.]\n", "  [0. 0. 0. 0. 0. 0.]\n", "  [0. 0. 0. 0. 0. 0.]]\n", "\n", " [[0. 0. 0. 0. 0. 0.]\n", "  [0. 0. 0. 0. 0. 0.]\n", "  [0. 0. 0. 0. 0. 0.]\n", "  [0. 0. 0. 0. 0. 0.]\n", "  [1. 1. 1. 0. 0. 0.]\n", "  [0. 1. 0. 0. 0. 0.]\n", "  [2. 2. 0. 0. 0. 0.]\n", "  [0. 0. 0. 0. 0. 0.]\n", "  [0. 0. 0. 0. 0. 0.]\n", "  [0. 0. 0. 0. 0. 0.]\n", "  [0. 0. 0. 0. 0. 0.]]\n", "\n", " [[0. 0. 0. 0. 0. 0.]\n", "  [0. 0. 0. 0. 0. 0.]\n", "  [0. 0. 0. 0. 0. 0.]\n", "  [0. 0. 0. 0. 0. 0.]\n", "  [1. 1. 0. 0. 0. 0.]\n", "  [1. 0. 0. 0. 0. 0.]\n", "  [2. 0. 0. 0. 0. 0.]\n", "  [0. 0. 0. 0. 0. 0.]\n", "  [0. 0. 0. 0. 0. 0.]\n", "  [0. 0. 0. 0. 0. 0.]\n", "  [0. 0. 0. 0. 0. 0.]]]\n", "\n", "6size 总窗口:[[[0. 0. 0. 0. 0. 0.]\n", "  [0. 0. 0. 0. 0. 0.]\n", "  [0. 0. 0. 0. 0. 0.]\n", "  [0. 0. 0. 0. 0. 0.]\n", "  [0. 0. 0. 0. 1. 1.]\n", "  [0. 0. 0. 0. 0. 1.]\n", "  [0. 0. 0. 2. 2. 2.]\n", "  [0. 0. 0. 0. 0. 0.]\n", "  [0. 0. 0. 0. 0. 0.]\n", "  [0. 0. 0. 0. 0. 0.]\n", "  [0. 0. 0. 0. 0. 0.]]\n", "\n", " [[0. 0. 0. 0. 0. 0.]\n", "  [0. 0. 0. 0. 0. 0.]\n", "  [0. 0. 0. 0. 0. 0.]\n", "  [0. 0. 0. 0. 0. 0.]\n", "  [0. 0. 0. 1. 1. 1.]\n", "  [0. 0. 0. 0. 1. 0.]\n", "  [0. 0. 2. 2. 2. 0.]\n", "  [0. 0. 0. 0. 0. 0.]\n", "  [0. 0. 0. 0. 0. 0.]\n", "  [0. 0. 0. 0. 0. 0.]\n", "  [0. 0. 0. 0. 0. 0.]]\n", "\n", " [[0. 0. 0. 0. 0. 0.]\n", "  [0. 0. 0. 0. 0. 0.]\n", "  [0. 0. 0. 0. 0. 0.]\n", "  [0. 0. 0. 0. 0. 0.]\n", "  [0. 0. 1. 1. 1. 0.]\n", "  [0. 0. 0. 1. 0. 0.]\n", "  [0. 2. 2. 2. 0. 0.]\n", "  [0. 0. 0. 0. 0. 0.]\n", "  [0. 0. 0. 0. 0. 0.]\n", "  [0. 0. 0. 0. 0. 0.]\n", "  [0. 0. 0. 0. 0. 0.]]\n", "\n", " [[0. 0. 0. 0. 0. 0.]\n", "  [0. 0. 0. 0. 0. 0.]\n", "  [0. 0. 0. 0. 0. 0.]\n", "  [0. 0. 0. 0. 0. 0.]\n", "  [0. 1. 1. 1. 0. 0.]\n", "  [0. 0. 1. 0. 0. 0.]\n", "  [2. 2. 2. 0. 0. 0.]\n", "  [0. 0. 0. 0. 0. 0.]\n", "  [0. 0. 0. 0. 0. 0.]\n", "  [0. 0. 0. 0. 0. 0.]\n", "  [0. 0. 0. 0. 0. 0.]]\n", "\n", " [[0. 0. 0. 0. 0. 0.]\n", "  [0. 0. 0. 0. 0. 0.]\n", "  [0. 0. 0. 0. 0. 0.]\n", "  [0. 0. 0. 0. 0. 0.]\n", "  [1. 1. 1. 0. 0. 0.]\n", "  [0. 1. 0. 0. 0. 0.]\n", "  [2. 2. 0. 0. 0. 0.]\n", "  [0. 0. 0. 0. 0. 0.]\n", "  [0. 0. 0. 0. 0. 0.]\n", "  [0. 0. 0. 0. 0. 0.]\n", "  [0. 0. 0. 0. 0. 0.]]\n", "\n", " [[0. 0. 0. 0. 0. 0.]\n", "  [0. 0. 0. 0. 0. 0.]\n", "  [0. 0. 0. 0. 0. 0.]\n", "  [0. 0. 0. 0. 0. 0.]\n", "  [1. 1. 0. 0. 0. 0.]\n", "  [1. 0. 0. 0. 0. 0.]\n", "  [2. 0. 0. 0. 0. 0.]\n", "  [0. 0. 0. 0. 0. 0.]\n", "  [0. 0. 0. 0. 0. 0.]\n", "  [0. 0. 0. 0. 0. 0.]\n", "  [0. 0. 0. 0. 0. 0.]]]\n", "\n", "检测到0个[0 1 1 1 1 0],每个价值10000,目前总计价值0\n", "\n", "检测到0个[0 2 2 2 2 0],每个价值-10010,目前总计价值0\n", "\n", "检测到0个[0 1 1 1 1 2],每个价值5000,目前总计价值0\n", "\n", "检测到0个[2 1 1 1 1 0],每个价值5000,目前总计价值0\n", "\n", "检测到0个[0 2 2 2 2 1],每个价值-5010,目前总计价值0\n", "\n", "检测到0个[1 2 2 2 2 0],每个价值-5010,目前总计价值0\n", "\n"]}, {"data": {"text/plain": ["np.int64(110)"]}, "execution_count": 26, "metadata": {}, "output_type": "execute_result"}], "source": ["evaluate(board,patterns)"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.13.5"}}, "nbformat": 4, "nbformat_minor": 5}