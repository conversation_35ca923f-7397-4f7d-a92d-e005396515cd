import numpy as np
from agent import Agent

class Search(Agent):
    def __init__(self, player):
        super().__init__(player)
        self.limit_depth = 2
        a = self.player
        b = self.opponent

        # 定义模式列表，每个元素为 (pattern, score)
        # 5*1 patterns:
        self.patterns = [
            [
                # 活二 (两端空)
            (np.array([0, a, a, 0]), 10),
            (np.array([0, b, b, 0]), -10),
            # 眠二 (一端被堵)
            (np.array([0, a, a, b]), 1),
            (np.array([b, a, a, 0]), 1),
            (np.array([0, b, b, a]), -1),
            (np.array([a, b, b, 0]), -1),

            ],
            [
            # 连五 (100000分)
            (np.array([a, a, a, a, a]), 100000),
            (np.array([b, b, b, b, b]), -100100),
            # 活三（2000分）
            (np.array([0, a, a, a, 0]), 200),
            (np.array([0, b, b, b, 0]), -200),

            # 眠三（500分）
            (np.array([b, a, a, a, 0]), 50),
            (np.array([0, a, a, a, b]), 50),
            (np.array([a, b, b, b, 0]), -51),
            (np.array([0, b, b, b, a]), -51) 
        ],
        # 6*1patterns
        [
            # 活四
            (np.array([0, a, a, a, a, 0]), 1000),
            (np.array([0, b, b, b, b, 0]), -2000),
            # 冲四
            (np.array([0, a, a, a, a, b]), 500),
            (np.array([b, a, a, a, a, 0]), 500),
            (np.array([0, b, b, b, b, a]), -501),
            (np.array([a, b, b, b, b, 0]), -501),
        ]]
    def make_move(self, board):
        # 第一层循环不会做任何剪枝
        next_pos = self.get_next(board)
        return next_pos[np.argmax(np.array(
            [0.9 * self.alpha_beta_search(
                0,
                self.adjust_board(pos,self.player,board),
                self.opponent) + 
             0.1 * self.alpha_beta_search(
                2,
                self.adjust_board(pos,self.player,board),
                self.opponent
             )
            for pos in next_pos]))]
    
    def get_next(self,board):
        # 如果棋局刚开始
        if np.all(board == np.zeros(board.shape)):
            return [(board.shape[0] // 2, board.shape[1] // 2)]
        # 将所有九宫格内有棋子的空位视为候选位置
        return [(i,j) 
                for j in range(0,board.shape[0]) 
                for i in range(0,board.shape[1])
                if  board[i][j] == 0 and
                 np.sum(board[max(0,i-1):i+2,max(0,j-1):j+2]) != 0]
    
    def alpha_beta_search(self,depth,board,current_player,alpha=float('-inf'),beta=float('inf')):
        if depth == self.limit_depth:
            return self.evaluate(board)
        next_pos = self.get_next(board)
        # 如果接下来是自家落子
        if current_player ==self.player:
            value = float('-inf')
            for pos in next_pos :
                child_value = self.alpha_beta_search(
                    depth + 1,
                    self.adjust_board(pos,current_player,board),
                    3 - current_player,
                    alpha,
                    beta
                )
                value = max(value, child_value)
                alpha = max(value, alpha)

                # 如果找到value比beta大的子节点，则舍弃这个父节点，因为对手不会选择这个节点
                if value >= beta:
                    break
            return value
        # 轮到对手落子
        else:
            value = float('inf')
            for pos in next_pos:
                child_value = self.alpha_beta_search(
                    depth + 1,
                    self.adjust_board(pos,current_player,board),
                    3 - current_player,
                    alpha,
                    beta
                )
                value = min(value,child_value)
                beta = min(beta,value)
                # 如果找到value比alpha小的子节点，则舍弃这个父节点，因为自己不会选择这个节点
                if value <= alpha:
                    break
            return value

    
    def adjust_board(self, pos, current_player, origin_board):
        if origin_board[pos[0]][pos[1]] != 0:
            print("Location is not valid!\n")
            return origin_board
        new_board = origin_board.copy()
        new_board[pos[0]][pos[1]] = current_player
        return new_board
    def evaluate(self,board:np.ndarray):
        # 创建滑动窗口
        def value_on_size(size_of_pattern):
            # 水平窗口
            window_1 = np.lib.stride_tricks.sliding_window_view(board,size_of_pattern,axis=1)
            # 垂直窗口
            window_2 = np.lib.stride_tricks.sliding_window_view(board,size_of_pattern,axis=0)
            # 斜窗口
            diagonal_board = [
                np.lib.stride_tricks.sliding_window_view(
                    np.diagonal(board,offset=k),size_of_pattern) 
                    for k in range(-board.shape[0] + size_of_pattern,board.shape[0] - size_of_pattern + 1)]
            diagonal_board += [
                np.lib.stride_tricks.sliding_window_view(
                    np.diagonal(np.fliplr(board),offset=k),size_of_pattern) 
                    for k in range(-board.shape[0] + size_of_pattern,board.shape[0] - size_of_pattern + 1)]
            window_3 = np.concatenate(diagonal_board,axis=0)
            window = np.concatenate((window_1.reshape(-1,size_of_pattern),window_2.reshape(-1,size_of_pattern),window_3.reshape(-1,size_of_pattern)),axis=0)
            sum_value = 0
            for pattern in self.patterns[size_of_pattern - 4]:
                sum_value += np.sum(np.all(window == pattern[0],axis=-1)) * pattern[1]
            return sum_value
        return value_on_size(4) + value_on_size(5) + value_on_size(6)
    
